import {
  DoubleMaterialityAssessmentData,
  ESGCategory,
  MaterialPillar,
} from '@apps/materiality-tracker/api/materiality-assessment';
import { Loader } from '@g17eco/atoms/loader';
import Chart from 'react-google-charts';
import { isDefined } from '@utils/index';
import './styles.scss';
import variables from '../../../../css/variables.module.scss';
import {
  chartStyles,
  chartConfigs,
  DEFAULT_CHART_COLORS,
  getChartEvents,
  LegendType,
  MATERIALITY_CHART_COLORS,
} from './chart-utils';
import { BasicAlert } from '@g17eco/molecules/alert';
import { isScoreInRange, NOT_FOUND_CATEGORY_MESSAGE } from './utils';

const WIDTH = '100%';
const HEIGHT = 240;
const COLUMN_WIDTH = '93%';

export const AssessmentInsightsDoubleMaterialityChart = ({
  data,
  selectedTopicCode,
  columnClickHandler,
  category,
}: {
  data: DoubleMaterialityAssessmentData[];
  selectedTopicCode: string;
  columnClickHandler: (index: number) => void;
  category: LegendType;
}) => {
  const config = chartConfigs.find((config) => config.code === category);

  if (!config) {
    return <BasicAlert type='warning'>{NOT_FOUND_CATEGORY_MESSAGE}</BasicAlert>;
  }

  const selectedIndex = data.findIndex((topic) => topic.code === selectedTopicCode);
  const chartColors = category === LegendType.Materiality ? MATERIALITY_CHART_COLORS : DEFAULT_CHART_COLORS;

  const getColor = (topic: DoubleMaterialityAssessmentData) => {
    let indexColor;
    switch (category) {
      case LegendType.ESG:
        indexColor = Object.values(ESGCategory).findIndex((code) => topic.categories?.esg?.includes(code));
        break;
      case LegendType.MaterialPillar:
        indexColor = Object.values(MaterialPillar).findIndex((code) =>
          topic.categories?.materialPillar?.includes(code),
        );
        break;
      case LegendType.Materiality: {
        indexColor = Object.values(config.categories).findIndex(({ min, max }) =>
          isScoreInRange({ score: topic.relativeScore, min, max }),
        );
        break;
      }
    }
    return `color: ${chartColors[indexColor ?? 0]}`;
  };

  const chartData = [
    ['Topic', 'Financial', { role: 'style' }, 'Impact', { role: 'style' }],
    ...data.map((topic, index) => {
      const color = getColor(topic);
      return [
        topic.name,
        topic.financialRelativeScore ?? NaN,
        `${color}; ${selectedIndex === index ? 'opacity: 1' : 'opacity: 0.25'}`,
        topic.nonFinancialRelativeScore ?? NaN,
        `${color}; ${selectedIndex === index ? 'opacity: 0.5' : 'opacity: 0.25'}`,
      ];
    }),
  ];

  const chartEvents = getChartEvents(columnClickHandler);

  const renderLegend = () => {
    return (
      <div className='d-flex justify-content-end ml-auto text-xs gap-2 mt-3 mb-2'>
        {Object.values(config.categories).map(({ label }, index) => (
          <div className='d-flex align-items-center gap-1' key={label}>
            <div className='legend-color-item' style={{ backgroundColor: chartColors[index ?? 0] }} />
            <span>{label}</span>
          </div>
        ))}
      </div>
    );
  };

  return (
    <div className='assessment__insights-chart'>
      {renderLegend()}
      <Chart
        chartType='ColumnChart'
        data={chartData}
        chartEvents={chartEvents}
        loader={<Loader />}
        options={{
          bar: {
            groupWidth: COLUMN_WIDTH,
          },
          chartArea: { ...chartStyles.chartArea, top: 0, bottom: 0, height: HEIGHT },
          hAxis: {
            textPosition: 'none',
          },
          vAxis: {
            ...chartStyles.axis,
            title: 'TOTAL TOPIC SCORE',
          },
          legend: 'none',
          baselineColor: variables.BgExtralight,
          isStacked: true,
        }}
        width={WIDTH}
        height={HEIGHT}
      />
    </div>
  );
};
