import { OrderedTopic } from '@apps/materiality-tracker/api/materiality-assessment';
import { PrioritizedAssessmentData } from '@apps/materiality-tracker/components/assessment-insights/types';

export const extendCustomTopics = (originalTopics: PrioritizedAssessmentData[], newOrderedTopics: OrderedTopic[]) => {
  const topicsMap = new Map(originalTopics.map((topic) => [topic.code, topic]));
  let priority = 1;
  return newOrderedTopics.reduce((topics, { code, disabled }) => {
    const topic = topicsMap.get(code);
    if (topic) {
      topics.push({ ...topic, priority: disabled ? -1 : priority, disabled });
      if (!disabled) {
        priority += 1;
      }
    }
    return topics;
  }, [] as PrioritizedAssessmentData[]);
};

export const NOT_FOUND_CATEGORY_MESSAGE = 'Invalid category. Please try a different selection.';

export const isScoreInRange = ({
  score,
  min,
  max,
}: {
  score: number | undefined;
  min: number | undefined;
  max: number | undefined;
}) => {
  return score !== undefined && (min ? score >= min : true) && (max ? score < max : true);
};
