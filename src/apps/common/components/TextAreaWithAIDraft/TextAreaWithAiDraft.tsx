/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import { useRef, useState, TextareaHTMLAttributes } from 'react'
import { useAutosizeTextArea } from '../../hooks/useAutosizeTextArea';
import { useTypewriter } from '../../hooks/useTypewriter';
import { Input } from 'reactstrap';
import { AIResponse } from '../../../../types/ai';
import { SiteAlertColors, addSiteAlert } from '../../../../slice/siteAlertsSlice';
import { AILoadingPlaceholder } from '../ai/AILoadingPlaceholder';
import { useAppDispatch, useAppSelector } from '../../../../reducers';
import { FeaturePermissions } from '../../../../services/permissions/FeaturePermissions';
import { ButtonGradient } from '@g17eco/atoms/button';
import { EditorState } from 'lexical';
import { RichTextEditor } from '@features/rich-text-editor';
import { AIDisclaimer } from '@g17eco/molecules/ai-disclaimer';
import { getRootConfig } from '@selectors/globalData';

interface AiDraft extends Omit<TextareaHTMLAttributes<HTMLTextAreaElement>, 'onChange'> {
  getDraft: () => Promise<Pick<AIResponse, 'content'>>;
  onChange: (value: string, editorState?: EditorState) => void;
  canUseRichTextEditor?: boolean;
  onEditorAIWrite?: (output: string) => void;
}

export const TextAreaWithAIDraft = (props: AiDraft) => {
  const { canUseRichTextEditor = false, onChange, onEditorAIWrite, name, className, maxLength, rows } = props;
  const dispatch = useAppDispatch();
  const [isLoading, setIsLoading] = useState(false);
  const [appendString, setAppendString] = useState<string | null>(null);
  const textAreaRef = useRef(null);

  const rootConfig = useAppSelector(getRootConfig);
  const hasAiTools = FeaturePermissions.canAccessAIDraftFurtherExplanation(rootConfig);

  const typewriter = useTypewriter({
    initialStr: `${props.value ?? ''}`,
    appendStr: appendString,
    onWrite: (output: string) => onChange(output),
    onEditorAIWrite: onEditorAIWrite,
  });

  const isWriting = typewriter.isWriting;
  const value = `${typeof props.value === 'string' ? (props.value ?? '') : ''}${typewriter.liveResult ?? ''}`;
  useAutosizeTextArea(textAreaRef.current, value);

  const getDraft = () => {
    if (!hasAiTools || isLoading) {
      return;
    }
    setIsLoading(true);
    props
      .getDraft()
      .then((response) => {
        const prefix = props.value ? '\n\n' : '';
        setAppendString(`${prefix}${response.content}`);
      })
      .catch(() => {
        dispatch(
          addSiteAlert({
            content: 'Unable to get AI draft. Please try again or contact our support team',
            color: SiteAlertColors.Danger,
          }),
        );
      })
      .finally(() => setIsLoading(false));
  };

  const isDrafting = isLoading || isWriting;
  const disabled = props.disabled || isDrafting;
  const showDisclaimer = Boolean(isDrafting || typewriter.finalResult);

  const onEditorChange = (plainText: string, editorState: EditorState) => {
    if (isDrafting) {
      return;
    }
    onChange(plainText, editorState);
  };

  return (
    <>
      <AILoadingPlaceholder isLoading={isLoading} isWriting={isWriting}>
        <div className='w-100 position-relative'>
          {canUseRichTextEditor ? (
            <RichTextEditor handleChange={({ plainText, editorState }) => onEditorChange(plainText, editorState)} />
          ) : (
            <Input
              type='textarea'
              innerRef={textAreaRef}
              name={name}
              className={`w-100 ${className} dont_translate`}
              maxLength={maxLength}
              rows={rows}
              onChange={(e) => {
                setAppendString('');
                onChange(e.target.value);
              }}
              disabled={disabled}
              value={value}
            />
          )}
          {hasAiTools ? (
            <div className='position-absolute' style={{ right: '10px', bottom: '10px' }}>
              <ButtonGradient disabled={disabled} color='gradient' outline={!isDrafting} onClick={() => getDraft()}>
                <i className='fal fa-sparkles mr-2' />
                {isDrafting ? 'Drafting...' : 'AI draft'}
              </ButtonGradient>
            </div>
          ) : null}
        </div>
      </AILoadingPlaceholder>
      {showDisclaimer ? <AIDisclaimer /> : null}
    </>
  );
};
