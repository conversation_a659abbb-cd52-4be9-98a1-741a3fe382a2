import { PreviousUtrv } from '@features/assistant';
import { g17ecoApi, transformResponse } from './g17ecoApi';
import { AdditionalContext, AIResponse } from '@g17eco/types/ai';
import { UniversalTrackerValuePlain } from '@g17eco/types/surveyScope';
import { AIAutoAnswerSurveyJobPlain, CreatedJob } from '@g17eco/types/ai-auto-answer-job';
import { BackgroundJob } from '@g17eco/types/background-jobs';

interface GetAIUtrvAssistantParams {
  utrvId: string;
  initiativeId: string;
  additionalContext?: AdditionalContext;
}

export interface AIUtrvSuggestion {
  predictedAnswer?: string | number | { [key: string]: string | number | string[] };
  questionExplanation: string;
  previousUtrvs: PreviousUtrv[];
  targetValue?: number;
  bestPractice: string[];
  keyInfo: string[];
  suggestedEvidence: {
    primaryDocumentation: string[];
    supportingDocumentation: string[];
  };
  whereToFind: {
    externalSource: string[];
    internalSource: string[];
  };
}

interface GetAIUtrvFurtherNotesDraftParams {
  initiativeId: string;
  data: {
    utrvId: string;
    draftData: Pick<UniversalTrackerValuePlain, 'value' | 'unit' | 'numberScale' | 'valueData'>;
  };
}

interface GetAIAutoAnswerSurveyParams {
  surveyId: string;
  initiativeId: string;
}

export enum SettingOption {
  DocumentLibrary = 'useDocumentLibrary',
  OverwriteMetric = 'isOverwriteMetric',
}

export type AIAutoAnswerSetting = {
  [key in SettingOption]: boolean;
}
interface PostAiAutoAnswerSurveyParams extends GetAIAutoAnswerSurveyParams {
  settings: AIAutoAnswerSetting;
}

const UTRV_ASSISTANT_TAG = 'ai-utrv-assistant';
const AUTO_ANSWER_SURVEY_TAG = 'ai-auto-answer-survey';
const DOCUMENT_SCAN_TAG = 'ai-document-scan';
const TAGS = [UTRV_ASSISTANT_TAG, AUTO_ANSWER_SURVEY_TAG, DOCUMENT_SCAN_TAG];

export const aiApi = g17ecoApi
  .enhanceEndpoints({
    addTagTypes: TAGS,
  })
  .injectEndpoints({
    endpoints: (builder) => ({
      getAIUtrvAssistant: builder.query<AIUtrvSuggestion, GetAIUtrvAssistantParams>({
        transformResponse,
        query: ({ initiativeId, utrvId, additionalContext }) => {
          return {
            url: `/initiatives/${initiativeId}/ai/utrv-assistant/${utrvId}`,
            method: 'post',
            data: additionalContext,
          };
        },
        providesTags: (_result, _error, agrs) => [{ type: UTRV_ASSISTANT_TAG, id: agrs.utrvId }],
      }),
      getAIUtrvFurtherNotesDraft: builder.mutation<AIResponse, GetAIUtrvFurtherNotesDraftParams>({
        transformResponse,
        query: ({ initiativeId, data }) => {
          return {
            url: `/initiatives/${initiativeId}/ai/generate-draft/further-notes`,
            method: 'post',
            data,
          };
        },
      }),
      getAIAutoAnswerSurvey: builder.query<{ job: AIAutoAnswerSurveyJobPlain }, GetAIAutoAnswerSurveyParams>({
        transformResponse,
        query: ({ initiativeId, surveyId }) => {
          return {
            url: `/initiatives/${initiativeId}/ai/survey/${surveyId}/auto-answer`,
            method: 'get',
          };
        },
        providesTags: (_result, _error, arg) => [{ type: AUTO_ANSWER_SURVEY_TAG, id: arg.surveyId }],
      }),
      aiAutoAnswerSurvey: builder.mutation<CreatedJob, PostAiAutoAnswerSurveyParams>({
        transformResponse,
        query: ({ initiativeId, surveyId, settings }) => {
          return {
            url: `/initiatives/${initiativeId}/ai/survey/${surveyId}/auto-answer`,
            method: 'post',
            data: settings,
          };
        },
        invalidatesTags: (_result, _error, arg) => [{ type: AUTO_ANSWER_SURVEY_TAG, id: arg.surveyId }],
      }),
      getAIDocumentScan: builder.query<{ job: Pick<BackgroundJob, '_id' | 'status'> | null }, string>({
        transformResponse,
        query: (initiativeId) => {
          return {
            url: `/initiatives/${initiativeId}/ai/document-scan`,
            method: 'get',
          };
        },
        providesTags: (_result, _error, arg) => [{ type: DOCUMENT_SCAN_TAG, id: arg }],
      }),
    }),
  });

export const {
  useGetAIUtrvAssistantQuery,
  useLazyGetAIUtrvAssistantQuery,
  useGetAIUtrvFurtherNotesDraftMutation,
  useGetAIAutoAnswerSurveyQuery,
  useAiAutoAnswerSurveyMutation,
  useGetAIDocumentScanQuery,
} = aiApi;
