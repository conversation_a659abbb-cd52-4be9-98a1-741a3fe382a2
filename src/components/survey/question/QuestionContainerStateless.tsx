/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import React, { useEffect, useMemo, useRef, useState } from 'react';
import { useAppDispatch, useAppSelector } from '../../../reducers';
import Question from './Question';
import { Prompt, useHistory, useLocation } from 'react-router-dom';
import { getUtrFromSurvey } from '../../../selectors/survey';
import { getCurrentUser, isOrgManager } from '../../../selectors/user';
import { Button } from 'reactstrap';
import { useQuestionForm } from './QuestionReducer';
import { CommentsInput } from '../../utr-confirmation-modal/Comments';
import { EvidenceInput } from '../../utr-confirmation-modal/EvidenceInput';
import { saveUniversalTrackerValue, UpdateActions } from '../../../actions/universalTrackerValue';
import {
  isAssuredLocked,
  isUtrvCompletedOpen,
} from '../../../utils/universalTrackerValue';
import { convertDataForSubmissions, prepareSubmitData } from './questionSubmit';
import QuestionSubmitButtons, { PrivacyButton } from './QuestionSubmitButtons';
import { checkHasComments, isDataEmpty } from './questionUtil';
import { NotApplicableTypes, UtrvStatus } from '../../../constants/status';
import { getSurveyAssurancePortfolios } from '../../../actions/assurance';
import { loadUtrvHistory } from '../../../actions/universalTracker';
import { handleRouteError, loggerMessage } from '../../../logger';
import { EvidenceFile, QuestionProps } from './questionInterfaces';
import { useHasCommentsEvidenceChanged, useHasUnitNumberScaleChanged, useHasValueChanged } from './useHasChanged';
import { generateDisabledUtrs, hasUTRVFilter } from '../utils/getDisableUtrs';
import { BlueprintContributions, ScopeQuestionGroup, SurveyType } from '../../../types/survey';
import { loadGlossary } from '../../../slice/glossarySlice';
import SDGContributionModal, { SDGContribution } from './SDGContributionModal';
import { SDGGoal, sdgMap } from '../../../constants/sdg-data';
import queryString from 'query-string';
import { CollapseButton, CollapseContent, CollapsePanel } from '@g17eco/molecules/collapse-panel';
import QuestionSubtitle from './toolbar/QuestionSubtitle';
import { getSubmitErrorMessage } from '../../../actions/error';
import UtrvComments from '../../utrv-comments';
import G17Client from '../../../services/G17Client';
import { SurveyActionData } from '../../../model/surveyData';
import { SessionStorage } from '../../../services/SessionStorage';
import './QuestionContainer.scss';
import { useQuestionIds } from '../../../hooks/useQuestionIds';
import { TOOLTIP } from '../../../constants/labels';
import { useGetCustomTagsQuery } from '../../../api/metric-groups';
import { TagList } from './tags/TagList';
import { FeaturePermissions } from '../../../services/permissions/FeaturePermissions';
import { QuestionRecommendations } from './QuestionRecommendations';
import { useBookmarks } from '../../survey-question-list/hooks/useBookmarks';
import { CongratulationsModal } from '../../complete-survey-modal/CongratulationsModal';
import { useToggleCompleteSurveys } from '../../../hooks/useToggleCompleteSurveys';
import { SurveyPermissions } from '../../../services/permissions/SurveyPermissions';
import { useToggleCongratulationsModal } from '../../complete-survey-modal/useToggleCongratulationsModal';
import { RichTextEditorContainer } from '@features/rich-text-editor';
import { NoteInstructionsViewer } from '../../../features/note-instructions';
import { Document } from 'flexsearch';
import NotFound from '../../not-found';
import { isAggregatedSurvey, isAutoAggregatedSurvey } from '../../../utils/survey';
import { usePartialAssuranceQuestionView } from '@hooks/usePartialAssuranceQuestionView';
import { skipToken } from '@reduxjs/toolkit/query';
import { useGetUtrvCommentsQuery } from '../../../api/utrv-comments';
import { AssistantBtn, ConnectionProvider } from '@features/assistant';
import { canEditUtrv } from '@features/universal-tracker-value';
import { SURVEY } from '@constants/terminology';
import { InitiativeUniversalTracker } from '@g17eco/types/initiativeUniversalTracker';
import { checkIsFieldRequired } from '@components/survey-question-list/utils';
import { useConditionality } from './useConditionality';
import { hasOverriddenUtrvConfig } from '@features/question-configuration';
import { checkHasInputWarningMessage } from '../utils/input';
import { useMappingAddons } from '@components/survey/question/mapping/useMappingAddons';
import { BasicAlert } from '@g17eco/molecules/alert';
import { LoadingPlaceholder } from '@g17eco/molecules/loader-container';
import { SimpleTooltip } from '@g17eco/molecules/simple-tooltip';
import { DraftQuestion } from './draft/DraftQuestion';
import configuration from '../../../config';
import { NotReportingModalContainer, OmissionReason } from './NotReportingModal';
import { useToggle } from '@hooks/useToggle';
import { generateToast } from '@components/toasts';
import { VariationConfirmModal } from './variation/VariationConfirmModal';
import { useVariationDetect } from './variation/useVariationDetect';
import { VariationContextProvider } from './variation/VariationContext';
import { QuestionStatusDisplay } from '@g17eco/molecules/question-status/QuestionStatusDisplay';
import { BlockingLoader } from '@g17eco/atoms/loader';
import { getRootConfig } from '@selectors/globalData';
import { AIAssistantContextProvider } from '@features/assistant/ai-assistant/AIAssistantContext';
import { TableInputProvider } from '@components/survey/form/input/table/TableInputContext';

const { UPDATE } = UpdateActions.STATUS;

interface QuestionContainerConfigInterface extends Pick<QuestionProps, 'enableDataHistory' | 'enableInformation'> {
  enableFurtherExplanation?: boolean;
  enableEvidence?: boolean;
  enableNANR?: boolean;
  enableMakePrivate?: boolean;
  enableQuestionSubtitle?: boolean;
  enableComments?: boolean;
  enableAIAssistant?: boolean;
  enableHeader?: boolean;
  enableCompletedMessage?: boolean;
}

interface QuestionContainerProps {
  initiativeId: string;
  surveyId: string;
  questionId: string;
  questionIndex: string;
  surveyGroups: ScopeQuestionGroup[];
  survey: SurveyActionData;
  blueprint: BlueprintContributions;
  searchIndex?: Document<unknown, false>;
  handleReload: () => Promise<any>;
  config?: QuestionContainerConfigInterface;
  isQuestionReadOnly?: boolean;
  rootInitiativeUtrMap?: Map<string, InitiativeUniversalTracker>;
  showMapping?: boolean;
  isDraft?: boolean;
  postSubmit?: (action: string) => void;
}

enum ValidationErrors {
  EvidenceRequired = 'You must supply evidence files to support your data',
  NoteRequired = 'You must add something in the Further explanation/Notes section',
}

interface SdgContributionModal {
  isOpen: boolean;
  contributionData?: SDGContribution;
}

const getConfig = (config?: QuestionContainerConfigInterface): Required<QuestionContainerConfigInterface> => {
  return {
    enableFurtherExplanation: config?.enableFurtherExplanation ?? true,
    enableEvidence: config?.enableEvidence ?? true,
    enableNANR: config?.enableNANR ?? true,
    enableMakePrivate: config?.enableMakePrivate ?? true,
    enableQuestionSubtitle: config?.enableQuestionSubtitle ?? true,
    enableComments: config?.enableComments ?? true,
    enableAIAssistant: config?.enableAIAssistant ?? true,
    enableHeader: config?.enableHeader ?? true,
    enableCompletedMessage: config?.enableCompletedMessage ?? true,
    enableDataHistory: config?.enableDataHistory ?? true,
    enableInformation: config?.enableInformation ?? true,
  };
};

export function QuestionContainerStateless(props: QuestionContainerProps) {
  const {
    initiativeId,
    surveyId,
    questionId,
    questionIndex,
    surveyGroups,
    survey,
    blueprint,
    searchIndex,
    handleReload,
    isQuestionReadOnly = false,
    rootInitiativeUtrMap,
    showMapping,
    postSubmit = () => {},
  } = props;

  const config = getConfig(props.config);
  const containerRef = useRef<HTMLDivElement>(null);
  // Only for default survey.
  const isDraftEnabled = configuration.features.draftMode && Boolean(props.isDraft) && survey.type === SurveyType.Default;

  // @TODO - Remove these hooks
  const history = useHistory();
  const location = useLocation();
  const dispatch = useAppDispatch();

  const ref = useRef<null | HTMLElement>(null);

  const scrollToRef = () => {
    if (ref.current) {
      ref.current.scrollIntoView({
        behavior: 'smooth',
        inline: 'nearest',
      });
    }
  };

  const user = useAppSelector(getCurrentUser);
  const isOrganizationManager = useAppSelector(isOrgManager);

  const isSurveyManager = !!(survey && user && SurveyPermissions.canManage(survey, user));
  const { bookmarks } = useBookmarks(surveyId);
  const { data: tags = [] } = useGetCustomTagsQuery(initiativeId, { skip: !initiativeId });
  const { utr, utrv } = getUtrFromSurvey({ survey, surveyGroups, questionId });

  const [openComments, setOpenComments] = useState(false);
  const [sdgContribution, setSDGContribution] = useState<SdgContributionModal>({
    isOpen: false,
    contributionData: undefined,
  });
  const [openNotReportingModal, toggleNotReportingModal, setNotReportingModal] = useToggle(false);

  const toggleContributionModal = () =>
    setSDGContribution({
      isOpen: !sdgContribution.isOpen,
      contributionData: undefined,
    });

  const [commentId, setCommentId] = useState('');

  const { toggleCompleteSurveys } = useToggleCompleteSurveys({
    handleReload,
    surveys: [survey],
    isCompleting: true,
  });

  const { isCongratulationsModalOpen, openCongratulationsModal, toggleCongratulationsModal } =
    useToggleCongratulationsModal({
      survey,
      currentUser: user,
      toggleCompleteSurveys,
    });

  const disableUtrs = useMemo(() => {
    const query = queryString.parse(location.search, { arrayFormat: 'bracket' });
    if (!hasUTRVFilter(query)) {
      return [];
    }

    return generateDisabledUtrs({
      surveyGroups,
      blueprint,
      searchIndex,
      user,
      ...query,
      filterByBookmarks: query.filterByBookmarks === 'true',
      bookmarks,
    });
  }, [blueprint, bookmarks, location.search, searchIndex, surveyGroups, user]);

  useEffect(() => {
    if (location.search) {
      const searchParams = new URLSearchParams(location.search);
      const id = searchParams.get('commentId');
      if (id) {
        setCommentId(id);
        setOpenComments(!!id);
        searchParams.delete('commentId');
        history.replace({
          pathname: location.pathname,
          search: searchParams.toString(),
        });
      }
    }
  }, [history, location.pathname, location.search]);

  const { currentQuestionIndex, altCode: alternativeCode } = useQuestionIds(
    surveyGroups,
    questionIndex,
    questionId,
    disableUtrs,
  );

  useEffect(() => SessionStorage.setQuestionIndex(currentQuestionIndex), [currentQuestionIndex]);

  const {
    state,
    handleComments,
    handleEvidenceLinkAdd,
    handleFilesAdded,
    handleFileRemoved,
    toggleExistingEvidence,
    changeQuestion,
    setDetails,
    update,
    updateTable,
    handleFileDescriptionAdd,
    handleAddToLibrary,
    setFiles,
  } = useQuestionForm();

  const {
    comments,
    editorState,
    saving,
    files,
    value,
    details,
    displayCheckbox,
    valueData,
    existingFiles,
    numberScale,
    table,
    unit,
    assurancePortfolio,
    assurancePortfolioLoaded,
    alternativeCodeOverride,
    canVerify,
    canContribute,
    inputMessage,
  } = state;

  const rootConfig = useAppSelector(getRootConfig);
  const canViewNoteInstructions = FeaturePermissions.canSetNoteInstructions(rootConfig);
  const canAccessAITools = FeaturePermissions.canAccessAIMetricAssistant(rootConfig) && config.enableAIAssistant;
  const { data: utrvComments, isLoading } = useGetUtrvCommentsQuery(utrv ? utrv._id : skipToken);
  const hasUtrvComments = Boolean(utrvComments?.items.length);

  const initiativeUtr = utr && rootInitiativeUtrMap?.get(utr.getId());

  const { variationWarnings, variationConfirmModalProps, handleOpenVariationConfirmModal } = useVariationDetect({
    initiativeUtr,
    utrvId: utrv?._id,
    utr: utr?.getRaw(),
    currentInputData: { value, valueData, table, numberScale, unit },
    unitConfig: survey.unitConfig,
  });

  React.useEffect(() => {
    if (user && utrv && utr) {
      changeQuestion({ utrv, utr, surveyData: survey, user });
      loadUtrvHistory(utrv._id)
        .then((respData: any) => setDetails({ data: respData, loaded: true }))
        .catch(() => setDetails({ loaded: true }));
    }
  }, [changeQuestion, utrv, utr, survey, setDetails, user]);

  React.useEffect(() => {
    getSurveyAssurancePortfolios(surveyId)
      .then((assurancePortfolio) => {
        update({
          assurancePortfolio,
          hasPortfolio: Boolean(assurancePortfolio?.assurances?.[0]),
          assurancePortfolioLoaded: true,
        });
      })
      .catch((e: Error) => {
        handleRouteError(e);
        update({ assurancePortfolioLoaded: true });
      });
  }, [surveyId, update]);

  React.useEffect(() => {
    dispatch(loadGlossary());
  }, [dispatch]);

  const { stakeholderHistory, verifierHistory } = React.useMemo(() => {
    const latestHistory = details.data?.latestHistory;
    if (!utrv?.notes) {
      return latestHistory ?? {};
    }

    return {
      stakeholderHistory: utrv.notes.stakeholder ?? latestHistory?.stakeholderHistory,
      verifierHistory: utrv.notes.verifier ?? latestHistory?.verifierHistory,
    };
  }, [details.data?.latestHistory, utrv?.notes]);

  const hasComments = checkHasComments(stakeholderHistory, verifierHistory);

  const isLoaded = user && blueprint && assurancePortfolioLoaded;
  const isUTRLoaded = utr && utrv;

  const conditionalityResult = useConditionality(utr?.getRaw().conditions);

  const isEmptyData = isDataEmpty({
    utr,
    displayCheckbox,
    valueData,
    value,
    table,
  });

  const hasValueChanged = useHasValueChanged(state, { utrv, utr, isEmptyData });
  const hasUnitNumberScaleChanged = useHasUnitNumberScaleChanged(state, { utrv, utr, surveyData: survey });
  const hasCommentsEvidenceChanged = useHasCommentsEvidenceChanged(state);
  const hasAnythingChanged = hasValueChanged || hasCommentsEvidenceChanged;

  const hasValidComments = utrv && isUtrvCompletedOpen(utrv) ? state.comments.trim() !== '' : true;

  const { addons: assuranceAddons } = usePartialAssuranceQuestionView(utr, utrv, assurancePortfolio);
  const { addons: mappingAddons } = useMappingAddons({ utr: utr?.getRaw(), initiativeId, enabled: showMapping });
  const addons = assuranceAddons.concat(mappingAddons);

  if (isLoaded && !isUTRLoaded) {
    // @TODO - Need a way to handle this?
    // No permission or bad UTR
    loggerMessage('No permission or bad UTR', {
      isLoaded,
      utrId: utr?.getId(),
      utrvId: utrv?._id,
    });
    return <NotFound />;
  }

  const isAggregate = isAggregatedSurvey(survey.type);
  const isAutoAggregated = isAutoAggregatedSurvey(survey.type);
  const isCompleted = Boolean(survey.completedDate);
  const enableComments = config.enableComments && !isAggregate;

  const isSpectator = !canEditUtrv({ status: utrv?.status, canContribute, canVerify });
  const canAutoVerify = canContribute && canVerify;

  const isQuestionDisabled = conditionalityResult.isDisabled || isQuestionReadOnly;
  const isReadOnly =
    saving || isSpectator || isAggregate || isCompleted || (utrv && isAssuredLocked(utrv)) || isQuestionDisabled;
  const selectedAltCode = alternativeCodeOverride || alternativeCode || utr?.getType() || '';

  const handleNA = (reason?: OmissionReason) => handleSubmitQuestion({ action: UPDATE, notApplicableType: NotApplicableTypes.na, autoVerify: canAutoVerify, omissionReason: reason });
  const handleNR = (reason?: OmissionReason) => handleSubmitQuestion({ action: UPDATE, notApplicableType: NotApplicableTypes.nr, autoVerify: canAutoVerify, omissionReason: reason });

  const handleAssuranceAction: QuestionProps['handleAssuranceAction'] = async (action) => {
    if (saving || !utrv || isCompleted) {
      return;
    }
    update({ saving: true, saveId: utrv._id, errored: false, message: '' });

    return G17Client.utrvAssuranceAction({ utrvId: utrv._id, action })
      .then(() => handleReload())
      .then(() =>
        update({
          saving: false,
          errored: false,
          message: 'Updated assurance status successfully',
        }),
      )
      .catch((e: Error) => {
        handleRouteError(e, { data: action, userId: user?._id });
        update({ saving: false, errored: true, message: e.message });
      });
  };

  const handlePrivacy = () => {
    if (!utrv) {
      return;
    }

    update({ saving: true, saveId: questionId, errored: false, message: '' });

    return G17Client.updateSurveyQuestionFlags({
      surveyId,
      utrvIds: [utrv._id],
      properties: { isPrivate: !utrv.isPrivate },
    })
      .then(() => handleReload())
      .then(() =>
        update({
          saving: false,
          errored: false,
          message: 'Saved Successfully',
          files: [],
          comments: '',
        }),
      )
      .catch((e: Error) => {
        handleRouteError(e);
        update({
          saving: false,
          errored: true,
          message: getSubmitErrorMessage(e, []),
        });
      });
  };

  const handleStandardsClick = (code: string) => update({ alternativeCodeOverride: code });

  const hasInvalidInput = inputMessage ? Object.keys(inputMessage).some((key) => inputMessage[key]) : false;

  const isFormValid = () => !isEmptyData && !hasInvalidInput;

  const isNoteRequired =
    utrv && (checkIsFieldRequired({ initiativeUtr, utrv, field: 'noteRequired' }) || isUtrvCompletedOpen(utrv));

  const isEvidenceRequired = utrv && checkIsFieldRequired({ initiativeUtr, utrv, field: 'evidenceRequired' });

  const isPrivate = utrv && checkIsFieldRequired({ initiativeUtr, utrv, field: 'isPrivate' });

  const isVerificationRequired = utrv && checkIsFieldRequired({ initiativeUtr, utrv, field: 'verificationRequired' });

  const handleSubmitWithVariationCheck = (action: string, notApplicableType: string = '', autoVerify = false) => {
    handleOpenVariationConfirmModal({
      action,
      handleSubmit: () => handleSubmitQuestion({ action, notApplicableType, autoVerify }),
    });
  };

  const handleSubmitQuestion = ({
    action,
    notApplicableType = '',
    omissionReason,
    autoVerify = false,
  }: {
    action: string;
    notApplicableType?: string;
    omissionReason?: OmissionReason;
    autoVerify?: boolean;
  }) => {
    if (saving || !utrv || !utr) {
      return false;
    }

    const filesToSubmit: EvidenceFile[] = [...files];
    const addToLibrary = files.some((f) => f.saveToLibrary);

    if (UPDATE === action && existingFiles.length > 0) {
      // Can only reuse UPDATED evidence
      filesToSubmit.push(...existingFiles.filter((f) => f.status === UtrvStatus.Updated && !f.isDeleted));
    }

    const isEvidenceInvalid = isEvidenceRequired && filesToSubmit.length < 1 && !notApplicableType;
    if (UPDATE === action && isEvidenceInvalid) {
      update({ saving: false, errored: true, message: ValidationErrors.EvidenceRequired });
      scrollToRef();
      return;
    }

    if (UPDATE === action && isNoteRequired && !comments && !notApplicableType) {
      update({ saving: false, errored: true, message: ValidationErrors.NoteRequired });
      scrollToRef();
      return;
    }

    const data = prepareSubmitData({
      utr,
      utrv,
      value,
      unit,
      numberScale,
      files: filesToSubmit,
      comments,
      editorState,
      valueData,
      table,
      displayCheckbox,
      autoVerify,
      notApplicableType,
      omissionReason
    });
    const questionId = utr.getId();
    const questionUtrvId = utrv._id;

    update({ saving: true, saveId: questionId, errored: false, message: '' });

    const questionData = convertDataForSubmissions(data);
    const saveUtr = dispatch(saveUniversalTrackerValue(action, questionUtrvId, questionData));

    saveUtr
      .then((result: { data: { data: SDGContribution[] } }) => {
        const goals: SDGContribution[] = [];
        const targets: SDGContribution[] = [];

        const sortFunction = (a: SDGContribution, b: SDGContribution) =>
          a.value - (a.oldValue ?? 0) - (b.value - (b.oldValue ?? 0));
        const goalsAndTargets: SDGGoal[] = sdgMap;
        const results = result.data.data;

        if (Array.isArray(results)) {
          results.forEach((item: SDGContribution) => {
            goalsAndTargets.forEach((code) => {
              if (item.universalTracker.code === `sdg/${code.code}`) {
                if (item.value - (item.oldValue ?? 0) >= 1) {
                  return goals.push(item);
                }
              }
            });

            goalsAndTargets.forEach((o) =>
              o.targets.forEach((code) => {
                if (item.universalTracker.code === `sdg/${code.code}`) {
                  if (item.value - (item.oldValue ?? 0) >= 1) {
                    return targets.push(item);
                  }
                }
              }),
            );
          });
          const highestGoal = goals?.sort(sortFunction)[0];
          const highestTarget = targets.sort(sortFunction)[0];

          setSDGContribution({
            isOpen: !!highestGoal || !!highestTarget,
            contributionData: highestGoal ?? highestTarget,
          });
        }
      })
      .then(() => handleReload())
      .then(() =>
        update({
          saving: false,
          errored: false,
          message: 'Saved Successfully',
          files: [],
          comments: '',
          editorState: undefined,
        }),
      )
      .then(() => {
        if (addToLibrary) {
          generateToast({
            title: 'Success',
            color: 'success',
            message: 'Successfully added to the Document Library',
          });
        }
        postSubmit(action);
      })
      .then(() => {
        openCongratulationsModal({ editingUtrvIds: [questionUtrvId], verified: questionData.autoVerify === 'true' });
      })
      .catch((e: Error) => {
        handleRouteError(e, { data: JSON.stringify(data), userId: user?._id });
        update({
          saving: false,
          errored: true,
          message: getSubmitErrorMessage(e, filesToSubmit),
        });
        scrollToRef();
      });
  };

  const handleSubmitUpdate = (e: React.MouseEvent, verify = false) => {
    e.preventDefault();
    const action = UPDATE;
    update({ action: action });
    return handleSubmitWithVariationCheck(action, '', verify);
  };

  const handleToggle = () => {
    setCommentId('');
    setOpenComments(!openComments);
  };

  const hasWarningMessage = checkHasInputWarningMessage({ utr, initiativeUtr, table, unit, numberScale });
  const disabledActionButtons = !isLoaded || isReadOnly || hasWarningMessage;
  const showPrivacyButton = utr && utrv && !isAggregate && !isCompleted && isSurveyManager;

  return (
    <>
      {saving ? <BlockingLoader /> : null}
      {isCompleted && config.enableCompletedMessage ? (
        <div className='mb-4 text-center'>
          This {SURVEY.SINGULAR} is marked as{' '}
          <span className='btn btn-success btn-sm'>
            <i className='fa fa-lock mr-2' />
            Completed
          </span>{' '}
          and can no longer be modified.
        </div>
      ) : null}
      <BasicAlert type='info'>{conditionalityResult.userMessage}</BasicAlert>
      {config.enableComments && utrv && openComments ? (
        <div className='position-relative' style={{ marginTop: '-3px' }}>
          <UtrvComments
            toggle={handleToggle}
            utrvId={utrv?._id}
            commentId={commentId}
            utrvComments={utrvComments}
            isLoading={isLoading}
            surveyId={surveyId}
          />
        </div>
      ) : null}
      <div className='questionContainer' ref={containerRef}>
        <ConnectionProvider currentInputData={{ value, valueData, table, numberScale, unit }}>
          <TableInputProvider>
            <>
              {config.enableHeader ? (
                <>
                  {isAggregate ? null : (
                    <div className='d-flex justify-content-between align-items-center question-header-container mb-4'>
                      {utrv && utr ? (
                        <div className='d-flex' data-testid={'question-container-header-button'}>
                          <AssistantBtn
                            key={utrv._id}
                            utrv={utrv}
                            utr={utr.getRaw()}
                            canAccessAITools={canAccessAITools}
                          />
                        </div>
                      ) : null}
                      <div className='d-none d-md-flex'>
                        <QuestionStatusDisplay utrv={utrv} isPrivate={isPrivate} />
                      </div>
                    </div>
                  )}

                  <div className='d-flex justify-content-between question-header-container'>
                    <div>
                      {config.enableQuestionSubtitle ? (
                        <LoadingPlaceholder height={17} isLoading={!utr}>
                          <QuestionSubtitle
                            utr={utr}
                            selectedStandard={selectedAltCode}
                            handleStandardsClick={handleStandardsClick}
                            survey={survey}
                          />
                        </LoadingPlaceholder>
                      ) : null}
                      <span ref={ref} style={{ top: '-6.5rem', position: 'relative' }} />
                    </div>
                    {enableComments ? (
                      <div className='ml-auto text-right'>
                        <div className='d-flex justify-content-end align-items-center'>
                          <div className='toolbar-button'>
                            <SimpleTooltip text={TOOLTIP.comment}>
                              <Button
                                color='transparent'
                                active={openComments}
                                className='toolbar-status-button comment-btn'
                                disabled={isAggregate}
                                onClick={handleToggle}
                              >
                                {hasUtrvComments ? (
                                  <i className='far fa-message-lines' />
                                ) : (
                                  <i className='far fa-comment-alt' />
                                )}
                              </Button>
                            </SimpleTooltip>
                            {showPrivacyButton ? (
                              <PrivacyButton
                                utrv={utrv}
                                disabled={disabledActionButtons}
                                handlePrivacy={
                                  config.enableMakePrivate &&
                                  !hasOverriddenUtrvConfig({ initiativeUtr, field: 'isPrivate' })
                                    ? handlePrivacy
                                    : null
                                }
                              />
                            ) : null}
                          </div>
                        </div>
                      </div>
                    ) : null}
                  </div>
                </>
              ) : null}

              <div className='d-flex d-md-none mt-3'>
                <QuestionStatusDisplay utrv={utrv} isPrivate={isPrivate} className='w-100' />
              </div>
              <AIAssistantContextProvider
                initiativeId={initiativeId}
                utrvId={utrv?._id}
                utr={utr?.getRaw()}
              >
                <VariationContextProvider variationWarnings={variationWarnings} utrvId={utrv?._id}>
                  {isDraftEnabled ? (
                    <DraftQuestion
                      key={questionId}
                      containerRef={containerRef}
                      users={utrvComments?.users}
                      {...state}
                      index={0}
                      currentUser={user}
                      hasChanged={hasAnythingChanged}
                      utr={utr}
                      utrv={utrv}
                      isVerifier={canVerify}
                      isContributor={canContribute}
                      isOrganizationManager={isOrganizationManager}
                      survey={survey}
                      update={update}
                      updateTable={updateTable}
                      alternativeCode={selectedAltCode}
                      isAggregate={isAggregate}
                      scrollToRef={scrollToRef}
                      handleNA={handleNA}
                      handleNR={handleNR}
                      handleAssuranceAction={handleAssuranceAction}
                      handleReject={() => handleSubmitQuestion({ action: UpdateActions.STATUS.REJECT })}
                      handleComments={handleComments}
                      hasValueChanged={hasValueChanged}
                      addons={{ after: addons }}
                      isQuestionReadOnly={isQuestionDisabled}
                      initiativeUtr={initiativeUtr}
                    />
                  ) : (
                    <Question
                      {...state}
                      index={0}
                      hasChanged={hasAnythingChanged}
                      utr={utr}
                      utrv={utrv}
                      isVerifier={canVerify}
                      isContributor={canContribute}
                      isOrganizationManager={isOrganizationManager}
                      survey={survey}
                      update={update}
                      updateTable={updateTable}
                      alternativeCode={selectedAltCode}
                      isAggregate={isAggregate}
                      scrollToRef={scrollToRef}
                      handleNA={handleNA}
                      handleNR={handleNR}
                      handleAssuranceAction={handleAssuranceAction}
                      handleReject={() => handleSubmitQuestion({ action: UpdateActions.STATUS.REJECT })}
                      handleComments={handleComments}
                      hasValueChanged={hasValueChanged}
                      addons={{ after: addons }}
                      isQuestionReadOnly={isQuestionDisabled}
                      initiativeUtr={initiativeUtr}
                      enableDataHistory={config.enableDataHistory}
                      enableInformation={config.enableInformation}
                    />
                  )}
                </VariationContextProvider>
              </AIAssistantContextProvider>
            </>
          </TableInputProvider>
        </ConnectionProvider>
        <QuestionRecommendations utr={utr?.getRaw()} />
        {isAutoAggregated ? (
          <div className='mt-4 text-center text-ThemeTextMedium'>
            This question cannot be modified as it is automatically aggregating its results using subsidiary data
          </div>
        ) : null}

        {isAggregate ? null : (
          <>
            {config.enableFurtherExplanation ? (
              <CollapsePanel className='comments mt-3 py-3 border-top' collapsed={!isNoteRequired && !hasComments}>
                <CollapseButton>
                  <h5 className='question-title-container text-ThemeAccentExtradark'>
                    Further explanation / notes {isNoteRequired ? '*' : ''}
                  </h5>
                </CollapseButton>
                <CollapseContent>
                  <div>
                    {canViewNoteInstructions ? (
                      <NoteInstructionsViewer
                        noteInstructionsEditorState={survey.noteInstructionsEditorState}
                        noteInstructions={survey.noteInstructions}
                      />
                    ) : null}
                    <div className='mt-3'>
                      <LoadingPlaceholder height={85} isLoading={!utr}>
                        {utr && utrv ? (
                          <RichTextEditorContainer>
                            <CommentsInput
                              key={utrv._id}
                              state={{
                                value: state.value,
                                valueData: state.valueData,
                                table: state.table,
                                numberScale: state.numberScale,
                                unit: state.unit,
                              }}
                              disabled={isReadOnly}
                              hideLabel
                              comments={comments}
                              isVerifier={canVerify}
                              isContributor={canContribute}
                              utrv={utrv}
                              stakeholderHistory={stakeholderHistory}
                              verifierHistory={verifierHistory}
                              handleComments={handleComments}
                            />
                          </RichTextEditorContainer>
                        ) : (
                          <></>
                        )}
                      </LoadingPlaceholder>
                    </div>
                  </div>
                </CollapseContent>
              </CollapsePanel>
            ) : null}

            {config.enableEvidence && utr && utrv ? (
              <LoadingPlaceholder height={172} isLoading={!utr}>
                <EvidenceInput
                  disabled={isReadOnly}
                  handleEvidenceLinkAdd={handleEvidenceLinkAdd}
                  handleFilesAdded={handleFilesAdded}
                  handleFileRemoved={handleFileRemoved}
                  files={files}
                  details={details}
                  isVerifier={canVerify}
                  isContributor={canContribute}
                  existingFiles={existingFiles}
                  evidenceInstructions={utr.getEvidenceInstructions(selectedAltCode)}
                  handleExistingFileRemoved={toggleExistingEvidence}
                  isEvidenceRequired={isEvidenceRequired}
                  handleFileDescriptionAdd={handleFileDescriptionAdd}
                  handleAddToLibrary={handleAddToLibrary}
                  initiativeId={initiativeId}
                  setFiles={setFiles}
                />
              </LoadingPlaceholder>
            ) : null}

            <div className='button-container mt-5' id='scrollConfirmVerify'>
              {conditionalityResult.userMessage ? (
                <BasicAlert type='info'>{conditionalityResult.userMessage}</BasicAlert>
              ) : null}
              {isCompleted && config.enableCompletedMessage ? (
                <div className='mt-4 text-center'>
                  This {SURVEY.SINGULAR} is marked as{' '}
                  <span className='btn btn-success btn-sm'>
                    <i className='fa fa-lock mr-2' />
                    Completed
                  </span>{' '}
                  and can no longer be modified.
                </div>
              ) : utr && utrv ? (
                <>
                  <QuestionSubmitButtons
                    isDraft={isDraftEnabled}
                    utrv={utrv}
                    utr={utr}
                    isVerifier={canVerify}
                    isContributor={canContribute}
                    hasAnythingChanged={hasAnythingChanged || hasUnitNumberScaleChanged}
                    hasValidComments={hasValidComments}
                    formValid={isFormValid()}
                    disabled={disabledActionButtons}
                    handleSubmitUpdate={handleSubmitUpdate}
                    handleSubmitQuestion={handleSubmitWithVariationCheck}
                    isVerificationRequired={!!isVerificationRequired}
                    handleNotReporting={config.enableNANR ? toggleNotReportingModal : null}
                  />
                  <NotReportingModalContainer
                    key={openNotReportingModal ? 'open-not-reporting' : 'close-not-reporting'}
                    isOpen={openNotReportingModal}
                    toggle={() => setNotReportingModal(false)}
                    utrv={utrv}
                    utr={utr}
                    comments={comments}
                    disabled={disabledActionButtons}
                    disabledNote={disabledActionButtons}
                    requiredNote={isNoteRequired}
                    handleNA={config.enableNANR ? handleNA : null}
                    handleNR={config.enableNANR ? handleNR : null}
                    handleComments={handleComments}
                  />
                </>
              ) : null}
            </div>
          </>
        )}
        <TagList tags={tags} utrId={utr?.getId() ?? ''} />
      </div>

      <CongratulationsModal isOpen={isCongratulationsModalOpen} toggle={toggleCongratulationsModal} />
      {variationConfirmModalProps && Object.values(variationConfirmModalProps).every(Boolean) ? (
        <VariationConfirmModal {...variationConfirmModalProps} />
      ) : null}
      <div>
        {sdgContribution.contributionData ? (
          <SDGContributionModal
            isOpen={sdgContribution.isOpen}
            toggle={toggleContributionModal}
            sdgContribution={sdgContribution.contributionData}
          />
        ) : null}
      </div>

      <Prompt
        when={hasAnythingChanged}
        message={() => 'Are you sure you want to leave this page? Your unconfirmed data will be lost.'}
      />
    </>
  );
}


