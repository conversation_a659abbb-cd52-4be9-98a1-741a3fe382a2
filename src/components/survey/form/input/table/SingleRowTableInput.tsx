/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */


import React, { useCallback, useMemo } from 'react';
import { InputColumn, TableInputProps } from './InputInterface';
import { TableInputForm } from './TableInputForm';
import { isColumnVisible, recalculateRowColumns, recalculateVisibilityRules, VisibilityResult } from './calculation';
import { RowStatus } from '../../../question/questionInterfaces';
import { generateDecimalErrorMessage } from '../../../utils/input';
import { getOverriddenTableColumns } from './tableUtils';
import { NumberScale } from '@g17eco/types/units';
import { useTableInputContext } from '@components/survey/form/input/table/TableInputContext';

export default function SingleRowTableInput(props: TableInputProps) {
  const { table, tableConfiguration, calculationColumns, visibilityRuleColumns, universalTracker, unitConfig } = props;

  // Move this to parent
  const firstRow = useMemo(() => {
    const { rows } = table;
    const initialRow = rows[0] ?? { rowStatus: RowStatus.added, id: 0, data: [] };
    const initialInputColumns = getOverriddenTableColumns(tableConfiguration.columns, unitConfig);

    // Go through each column and initial missing columns
    initialRow.data = initialInputColumns.map((initialInputColumn) => {
      const existingColumn = initialRow.data.find((c) => c.code === initialInputColumn.code);
      if (!existingColumn) {
        return initialInputColumn;
      }
      const isAnswered = existingColumn.value !== undefined && existingColumn.value !== '';
      /**
       * If column is unanswered => return column
       * If it is answered and no numberScale provided, then use numberScale from tableConfiguration if it exists
       * If numberScale from tableConfiguration is not set and input override is single, then safe to assume it is single
       * Otherwise let the user select
       */
      if (!isAnswered) {
        return existingColumn;
      }

      if (!existingColumn.numberScale) {
        const tableConfigColumn = tableConfiguration.columns.find((c) => c.code === existingColumn.code);
        if (tableConfigColumn && tableConfigColumn.numberScale) {
          existingColumn.numberScale = tableConfigColumn.numberScale;
        } else if (initialInputColumn.numberScale === NumberScale.Single) {
          existingColumn.numberScale = initialInputColumn.numberScale;
        }
      }
      return existingColumn;
    });
    return initialRow;
  }, [table, tableConfiguration.columns, unitConfig]);

  const { formRow, setFormRowData } = useTableInputContext();
  const [visibilityResults, setVisibilityResults] = React.useState<VisibilityResult>();

  const setFormRow = useCallback((data: InputColumn[]) => {
    const {visibleColumns, visibleResults} = recalculateVisibilityRules(data, visibilityRuleColumns, {} )
    setVisibilityResults(visibleResults)
    setFormRowData(
      visibleColumns
    );
  }, [setFormRowData, visibilityRuleColumns]);

  // Initialize columns validation
  React.useEffect(() => {
    setFormRow(firstRow.data);
  }, [tableConfiguration, setFormRow, firstRow.data]);

  return (
    <div className='table-input single-row'>
      <TableInputForm
        {...props}
        row={formRow}
        multiRow={false}
        isColumnVisible={(code: string) => isColumnVisible(code, visibilityResults)}
        updateColumn={(column: InputColumn) => {
          const nextRowData = recalculateRowColumns(formRow, column, calculationColumns);
          setFormRow(nextRowData)
          const inputMessage = generateDecimalErrorMessage({
            utr: universalTracker,
            input: Object.fromEntries(nextRowData.map((k) => [[k.code], k.value])),
          });
          props.updateTable({ rows: [{ ...firstRow, data: nextRowData }] }, inputMessage)
        }}
      />
    </div>
  );
}
