/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */


import React, { useCallback, useState } from 'react';
import { InputColumn, TableInputProps } from './InputInterface';
import { TableInputForm } from './TableInputForm';
import { isColumnVisible, recalculateRowColumns, recalculateVisibilityRules, VisibilityResult } from './calculation';
import { getOverriddenTableColumns, isDisabled, transformRowsOnAddOrUpdate, updateRowData } from './tableUtils';
import { Button, Collapse } from 'reactstrap';
import TableInputView from '../../view/TableInputView';
import { RowDataInfo, RowStatus } from '../../../question/questionInterfaces';
import { TableErrorModal } from './partial/TableErrorModal';
import './MultiRowTableInput.scss';
import {
  generateDecimalErrorMessage,
  getRowValidationItems,
  getTableInputColumnWarningMessage
} from '../../../utils/input';
import { useGetAggregatedFormRow } from './hooks/useGetAggregatedFormRow';
import { MultiRowTableProvider } from '@components/survey/form/input/table/MultiRowTableContext';
import { useVariationContext } from '@components/survey/question/variation/VariationContext';
import { FeatureStability } from '@g17eco/molecules/feature-stability';
import { useTableInputContext } from '@components/survey/form/input/table/TableInputContext';

const commonBtnClasses = 'w-100 rounded-0 d-flex justify-content-center align-items-center';

export default function MultiRowTableInput(props: TableInputProps) {
  const {
    table,
    updateTable,
    tableConfiguration,
    calculationColumns,
    visibilityRuleColumns,
    scrollToRef,
    universalTracker,
    unitConfig,
    initiativeUtr,
  } = props;
  const disabled = props.isDisabled?.();
  const { rows: rowDataInfo, editRowId } = table;
  const { formRow, setFormRowData } = useTableInputContext();
  const [visibilityResults, setVisibilityResults] = React.useState<VisibilityResult>();
  const [showEmptyTableErrorModal, setShowEmptyTableErrorModal] = useState<boolean>(false);

  const [isAdding, setIsAdding] = useState(true);

  const setFormRow = useCallback((data: InputColumn[]) => {
    const { visibleColumns: row, visibleResults: visibilityResults } = recalculateVisibilityRules(data, visibilityRuleColumns, {})
    setVisibilityResults(visibilityResults)
    setFormRowData(
      row
    );
  }, [setFormRowData, visibilityRuleColumns]);

  // Initialize columns validation
  React.useEffect(() => {
    const initialInputColumns = getOverriddenTableColumns(tableConfiguration.columns, unitConfig);
    setFormRow(initialInputColumns);
  }, [tableConfiguration, setFormRow, unitConfig]);

  const utr = universalTracker.getRaw();
  const { utrvId, variationWarnings } = useVariationContext();
  const aggregatedTableData = useGetAggregatedFormRow({ utrvId, utr, initiativeUtr, table, editRowId, formRow });

  const setRowData = (rows: RowDataInfo[]) => updateTable({ rows });
  const setRowEditId = (id: number | string) => updateTable({ editRowId: Number(id) });

  const resetEditRow = () =>
    updateTable({
      rows: updateRowData(rowDataInfo, { id: editRowId, isEdited: false }),
      editRowId: -1,
    });

  const clearInputRow = () => {
    const initialInputColumns = getOverriddenTableColumns(tableConfiguration.columns, unitConfig);
    setFormRow(initialInputColumns);
  };

  const clearData = () => {
    resetEditRow();
    clearInputRow();
    setIsAdding(false);
  };

  const onAdd = () => {
    const newRow = {
      rowStatus: RowStatus.added,
      id: rowDataInfo.length,
      data: transformRowsOnAddOrUpdate(formRow),
    };
    updateTable({ rows: [...rowDataInfo, newRow], editRowId: -1 });
    clearInputRow();
    setIsAdding(false);
  };

  function onEdit() {
    clearInputRow();
    updateTable({
      rows: updateRowData(rowDataInfo, {
        id: editRowId,
        data: transformRowsOnAddOrUpdate(formRow),
        hasChanged: true,
        isEdited: false,
      }),
      editRowId: -1,
    });
  }

  // Only decimal
  const inputMessage = generateDecimalErrorMessage({
    utr: universalTracker,
    input: Object.fromEntries(formRow.map((k) => [[k.code], k.value])),
  });

  // This is dealing with unit/number scale warning messages
  const hasNumberScaleOrUnitWarning = formRow.some((inputColumn) => getTableInputColumnWarningMessage({
    initiativeUtr,
    inputColumn
  }));

  const addBtnDisabled =
    isDisabled(tableConfiguration.columns, formRow) ||
    (!!inputMessage && Object.keys(inputMessage).some((k) => inputMessage[k]));

  const handleClearDeleteRow = (row: RowDataInfo) => {
    setRowData(
      updateRowData(rowDataInfo, {
        id: row.id,
        isRemoved: false,
      })
    );
  };

  const rowValidations = disabled ? undefined : rowDataInfo.map((row) => {
    return {
      id: row.id,
      items: getRowValidationItems({
        inputColumns: row.data,
        initiativeUtr: initiativeUtr,
        variationWarnings,
        utr: props.universalTracker
      }),
    };
  });

  const tableInputViewProps = {
    ...props,
    editRowId,
    disabled,
    rowData: rowDataInfo,
    handleEditRow: (row: RowDataInfo) => {
      setIsAdding(true)
      setFormRow([...row.data]);
      setRowData(
        updateRowData(rowDataInfo, {
          id: row.id,
          isRemoved: false,
        })
      );
      setRowEditId(row.id);
      scrollToRef();
    },
    handleCopyRow: (row: RowDataInfo) => {
      setIsAdding(true)
      resetEditRow();
      setFormRow([...row.data]);
      scrollToRef();
    },
    handleDeleteRow: (row: RowDataInfo) => {
      clearData();
      if (row.rowStatus === RowStatus.added) {
        return setRowData(rowDataInfo.filter(({ id }) => row.id !== id));
      }
      setRowData(updateRowData(rowDataInfo, { id: row.id, isRemoved: true }));
      const remainingRows = rowDataInfo.filter((r) => !r.isRemoved && r.id !== row.id);
      if (remainingRows.length === 0) {
        setShowEmptyTableErrorModal(true);
      }
    },
    handleClearDeleteRow,
    unitConfig,
  };

  const mergedProps = {
    ...props,
    inputMessage,
    tableConfiguration,
    calculationColumns,
    multiRow: true,
    // override internal
    row: formRow,
    isColumnVisible: (code: string) => isColumnVisible(code, visibilityResults),
    updateColumn: (column: InputColumn) => {
      setFormRow(
        recalculateRowColumns(formRow, column, calculationColumns)
      );
    },
  };

  return (
    <MultiRowTableProvider
      initiativeUtr={initiativeUtr}
      aggregatedTableData={aggregatedTableData}
      utr={utr}
      utrvId={utrvId}
    >
      <div className='table-input multi-row'>
        <TableInputView {...tableInputViewProps} validations={rowValidations} />
        <Collapse isOpen={!disabled && isAdding}>
          <div className='table-input__form-container'>
            <i onClick={clearData} className='fa-solid fa-xmark table-input__close-btn'></i>
            <TableInputForm {...mergedProps} />
          </div>
          {mergedProps.isDisabled?.() ? (
            <></>
          ) : (
            <>
              {editRowId === -1 ? (
                <Button
                  color='primary'
                  className={`${commonBtnClasses} border-0 table-input__save-btn`}
                  disabled={addBtnDisabled}
                  onClick={onAdd}
                >
                  <i className='fa fa-plus mr-2' />
                  Save to table
                </Button>
              ) : (
                <Button
                  color='primary'
                  className={`${commonBtnClasses} border-0 table-input__save-btn`}
                  disabled={addBtnDisabled || hasNumberScaleOrUnitWarning}
                  onClick={onEdit}
                >
                  <i className='fa fa-plus mr-2' />
                  Update row
                </Button>
              )}
              <Button outline onClick={clearData} className={`${commonBtnClasses} table-input__cancel-btn`}>
                <i className='fa-solid fa-angle-up mr-2'></i>
                Cancel
              </Button>
            </>
          )}
        </Collapse>
        {!isAdding && (
          <Button outline onClick={() => setIsAdding(true)} className={`${commonBtnClasses} table-input__add-btn`}>
            <i className='fas fa-plus fa-2x mr-2' />
            Add row
          </Button>
        )}
        {variationWarnings.length ? (
          <div className='text-ThemeDangerMedium mt-1'>
            <FeatureStability stability='beta' />
            <strong className='ml-2'>Warning: </strong>
            <span>Some of the values entered falls outside of the variance limit set by the admin.</span>
          </div>
        ) : null}

        {showEmptyTableErrorModal ? (
          <TableErrorModal setShowEmptyTableErrorModal={setShowEmptyTableErrorModal} {...props} />
        ) : null}
      </div>
    </MultiRowTableProvider>
  );
}
